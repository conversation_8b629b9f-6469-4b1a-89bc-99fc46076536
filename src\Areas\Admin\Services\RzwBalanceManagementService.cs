using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using RazeWinComTr.Models;

namespace RazeWinComTr.Areas.Admin.Services;

/// <summary>
/// Service for managing RZW-specific balance operations including savings locks
/// </summary>
public class RzwBalanceManagementService
{
    private readonly AppDbContext _context;
    private readonly IWalletService _walletService;
    private readonly ITokenPriceService _tokenPriceService;
    private readonly ITradeService _tradeService;

    public RzwBalanceManagementService(
        AppDbContext context,
        IWalletService walletService,
        ITokenPriceService tokenPriceService,
        ITradeService tradeService)
    {
        _context = context;
        _walletService = walletService;
        _tokenPriceService = tokenPriceService;
        _tradeService = tradeService;
    }

    /// <summary>
    /// Gets the RZW token ID dynamically from TokenPriceService
    /// </summary>
    /// <returns>The RZW token ID</returns>
    public async Task<RzwTokenInfo> GetRzwTokenInfoAsync()
    {
        return await _tokenPriceService.GetRzwTokenInfoAsync();
    }

    /// <summary>
    /// Checks if a user has sufficient available RZW balance for a transaction
    /// </summary>
    /// <param name="userId">The user ID</param>
    /// <param name="amount">The amount to check</param>
    /// <returns>True if sufficient balance exists, false otherwise</returns>
    public async Task<bool> HasSufficientAvailableRzwAsync(int userId, decimal amount)
    {
        var rzwTokenInfo = await GetRzwTokenInfoAsync();
        var availableBalance = await _walletService.GetUserAvailableBalanceAsync(userId, rzwTokenInfo.TokenId);
        return availableBalance >= amount;
    }

    /// <summary>
    /// Gets comprehensive RZW balance information for a user
    /// </summary>
    /// <param name="userId">The user ID</param>
    /// <returns>RZW balance information</returns>
    public async Task<RzwBalanceInfo> GetRzwBalanceInfoAsync(int userId)
    {
        var rzwTokenInfo = await GetRzwTokenInfoAsync();
        var walletBalanceInfo = await _walletService.GetWalletBalanceInfoAsync(userId, rzwTokenInfo.TokenId);

        var rzwBalanceInfo = new RzwBalanceInfo
        {
            UserId = userId,
            AvailableRzw = walletBalanceInfo.AvailableBalance,
            LockedRzw = walletBalanceInfo.LockedBalance,
            ActiveSavingsAccountCount = walletBalanceInfo.ActiveSavingsAccountCount
        };

        return rzwBalanceInfo;
    }

    /// <summary>
    /// Locks RZW tokens for savings purposes using an existing transaction
    /// </summary>
    /// <param name="userId">The user ID</param>
    /// <param name="amount">The amount to lock</param>
    /// <param name="description">Description for the audit trail</param>
    /// <param name="transaction">Existing transaction to use</param>
    /// <param name="rzwSavingsAccountId">RZW Savings Account ID for tracking</param>
    /// <returns>True if successful, false if insufficient balance</returns>
    public async Task<bool> LockRzwForSavingsAsync(int userId, decimal amount, string description, AppDbContext? existingContext = null, int? rzwSavingsAccountId = null)
    {
        var contextToUse = existingContext ?? _context;
        var rzwTokenInfo = await GetRzwTokenInfoAsync();

        // Check if user has sufficient available RZW
        if (!await HasSufficientAvailableRzwAsync(userId, amount))
        {
            return false;
        }

        // Lock the balance using existing transaction
        var success = await _walletService.LockBalanceAsync(userId, rzwTokenInfo, amount, contextToUse);
        if (!success)
        {
            return false;
        }

        // Create specific trade record for savings lock
        var trade = new Trade
        {
            UserId = userId,
            CoinId = rzwTokenInfo.TokenId,
            Type = TradeType.RzwSavingsDeposit,
            CoinAmount = amount * -1,
            TryAmount = 0,
            CoinRate = 0,
            PreviousBalance = 0, // Will be set by wallet service
            NewBalance = 0, // Will be set by wallet service
            PreviousCoinBalance = 0,
            NewCoinBalance = 0,
            PreviousWalletBalance = 0,
            NewWalletBalance = 0,
            RzwSavingsAccountId = rzwSavingsAccountId,
            CreatedDate = DateTime.UtcNow,
            IsActive = true
        };
        await _tradeService.CreateAsync(trade, contextToUse);
        return true;
    }

    /// <summary>
    /// Unlocks RZW tokens from savings using an existing transaction
    /// </summary>
    /// <param name="userId">The user ID</param>
    /// <param name="amount">The amount to unlock</param>
    /// <param name="description">Description for the audit trail</param>
    /// <param name="existingContext">Existing transaction to use</param>
    /// <param name="rzwSavingsAccountId">RZW Savings Account ID for tracking</param>
    /// <param name="tradeType">Trade type for the transaction</param>
    /// <returns>True if successful, false if insufficient locked balance</returns>
    public async Task<bool> UnlockRzwFromSavingsAsync(int userId, decimal amount, string description, AppDbContext? existingContext = null, int? rzwSavingsAccountId = null, TradeType tradeType = TradeType.RzwSavingsWithdrawal)
    {
        var contextToUse = existingContext ?? _context;
        var rzwTokenInfo = await GetRzwTokenInfoAsync();

        // Unlock the balance using existing transaction
        var success = await _walletService.UnlockBalanceAsync(userId, rzwTokenInfo, amount, contextToUse);
        if (!success)
        {
            return false;
        }

        // Create specific trade record for savings unlock
        var trade = new Trade
        {
            UserId = userId,
            CoinId = rzwTokenInfo.TokenId,
            Type = tradeType,
            CoinAmount = amount,
            TryAmount = 0,
            CoinRate = 0,
            PreviousBalance = 0, // Will be set by wallet service
            NewBalance = 0, // Will be set by wallet service
            PreviousCoinBalance = 0,
            NewCoinBalance = 0,
            PreviousWalletBalance = 0,
            NewWalletBalance = 0,
            RzwSavingsAccountId = rzwSavingsAccountId,
            CreatedDate = DateTime.UtcNow,
            IsActive = true
        };

        await _tradeService.CreateAsync(trade, contextToUse);
        return true;
    }

    /// <summary>
    /// Adds RZW tokens as interest payment to user's available balance using an existing transaction
    /// </summary>
    /// <param name="userId">The user ID</param>
    /// <param name="amount">The interest amount to add</param>
    /// <param name="description">Description for the audit trail</param>
    /// <param name="transaction">Existing transaction to use</param>
    /// <param name="rzwSavingsAccountId">RZW Savings Account ID for tracking</param>
    /// <returns>The updated wallet</returns>
    public async Task<Wallet> AddRzwInterestAsync(int userId, decimal amount, string description, AppDbContext? existingContext = null, int? rzwSavingsAccountId = null)
    {
        var contextToUse = existingContext ?? _context;
        var rzwTokenInfo = await GetRzwTokenInfoAsync();

        // Add to available balance using the existing transaction
        var wallet = await _walletService.AddAvailableBalanceAsync(userId, rzwTokenInfo, amount, TradeType.RzwSavingsInterest, contextToUse);

        // Create specific trade record for interest payment
        var trade = new Trade
        {
            UserId = userId,
            CoinId = rzwTokenInfo.TokenId,
            Type = TradeType.RzwSavingsInterest,
            CoinAmount = amount,
            TryAmount = 0,
            CoinRate = 0,
            PreviousBalance = 0, // Will be set by wallet service
            NewBalance = 0, // Will be set by wallet service
            PreviousCoinBalance = 0,
            NewCoinBalance = 0,
            PreviousWalletBalance = 0,
            NewWalletBalance = 0,
            RzwSavingsAccountId = rzwSavingsAccountId,
            CreatedDate = DateTime.UtcNow,
            IsActive = true
        };

        contextToUse.Trades.Add(trade);
        await contextToUse.SaveChangesAsync();
        return wallet;
    }

    /// <summary>
    /// Deducts RZW tokens from user's available balance
    /// </summary>
    /// <param name="userId">The user ID</param>
    /// <param name="amount">The amount to deduct</param>
    /// <returns>True if successful, false if insufficient balance</returns>
    public async Task<bool> DeductAvailableRzwAsync(int userId, decimal amount)
    {
        var rzwTokenId = await GetRzwTokenInfoAsync();
        return await _walletService.DeductAvailableBalanceAsync(userId, rzwTokenId, amount);
    }

    /// <summary>
    /// Deducts RZW tokens from user's available balance using an existing transaction
    /// </summary>
    /// <param name="userId">The user ID</param>
    /// <param name="amount">The amount to deduct</param>
    /// <param name="transaction">Existing transaction to use</param>
    /// <returns>True if successful, false if insufficient balance</returns>
    public async Task<bool> DeductAvailableRzwAsync(int userId, decimal amount, AppDbContext? existingContext = null)
    {
        var rzwTokenId = await GetRzwTokenInfoAsync();
        return await _walletService.DeductAvailableBalanceAsync(userId, rzwTokenId, amount, existingContext);
    }
}
